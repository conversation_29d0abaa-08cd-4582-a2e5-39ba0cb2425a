# Default values for dify.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

global:
  # The edition of the application, SELF_HOSTED or CLOUD
  edition: "SELF_HOSTED"
  # A secret key that is used for securely signing the session cookie and encrypting sensitive information on the database. You can generate a strong key using `openssl rand -base64 42`.
  appSecretKey: 'swLe9W2pG6bc5vfefDylbqqHfcTuq4nr2nCoQwpSEyk='
  useTLS: true
  # The backend domain of the console API, used to concatenate the authorization callback.
  # If empty, it is the same domain. Example: console.dify.ai
  consoleApiDomain: "test-ookoo-platform.is.panasonic.cn"
  # The front-end domain of the console web, used to concatenate some front-end addresses and for CORS configuration use.
  # If empty, it is the same domain. Example: console.dify.ai
  consoleWebDomain: "test-ookoo-platform.is.panasonic.cn"
  # Service API domain, used to display Service API Base Url to the front-end.
  # If empty, it is the same domain. Example: api.dify.ai
  serviceApiDomain: "test-ookoo.is.panasonic.cn"
  # WebApp API backend domain, used to declare the back-end URL for the front-end API.
  # If empty, it is the same domain. Example: app.dify.ai
  appApiDomain: "test-ookoo.is.panasonic.cn"
  # WebApp domain, used to display WebAPP API Base Url to the front-end. If empty, it is the same domain. Example: api.app.dify.ai
  appWebDomain: "test-ookoo.is.panasonic.cn"
  # File preview or download domain, used to display the file preview
  # or download URL to the front-end or as a multi-modal model input;
  # In order to prevent others from forging, the image preview URL is signed and has a 5-minute expiration time.
  filesDomain: "test-ookoo-file.is.panasonic.cn"
  # Enterprise service domain, used to declare the back-end URL for the front-end API.
  enterpriseDomain: "test-ookoo-console.is.panasonic.cn"
  # When enabled, migrations will be executed prior to application startup and the application will start after the migrations have completed.
  dbMigrationEnabled: true
  rag:
    # RAG ETL type, support: dify or Unstructured
    etlType: "Unstructured"
    keywordDataSourceType: "object_storage"

ingress:
  enabled: true
  className: "nginx"
  annotations: 
    nginx.ingress.kubernetes.io/proxy-body-size: "15m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
  tls: 
    - hosts:
      - test-ookoo-platform.is.panasonic.cn
      - test-ookoo.is.panasonic.cn
      - test-ookoo-console.is.panasonic.cn
      secretName: panasonic-tls

api:
  enabled: true
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-api"
    # override appVersion in Chart.yaml if not empty
    tag: ""
  replicas: 1
  resources:
    limits:
      cpu: 4000m
      memory: 10240Mi
    requests:
      cpu: 1334m
      memory: 3414Mi
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /health
      port: 5001
      scheme: HTTP
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 3
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /health
      port: 5001
      scheme: HTTP
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 3
  extraEnv:
    - name: DATASET_OPERATOR_ENABLED
      value: "true"
    - name: HTTP_PROXY
      value: http://************:8080
    - name: HTTPS_PROXY
      value: http://************:8080
    - name: NO_PROXY
      value: "localhost,127.0.0.1,.svc,.svc.cluster.local,dify-sandbox-svc.default.svc.cluster.local,dify-plugin-daemon-svc,dify-plugin-daemon-svc.default.svc.cluster.local,************,************,*************,10.199.139,************,***********,*************,*************,************,************,************,*************,************,***********,http://dify-sandbox-svc:8194,http://dify-unstructured-svc:8000,http://dify-plugin-daemon-svc:5002"
    # - name: UNSTRUCTURED_API_URL
    #   value: http://dify-unstructured-svc:8000/general/v0/general
    - name: INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH
      value: "5000"
    - name: REDIS_SENTINEL_SOCKET_TIMEOUT
      value: "5"
    - name: HTTP_REQUEST_MAX_READ_TIMEOUT
      value: "300"
    - name: ENTERPRISE_ENABLED
      value: "true"
    - name: UPLOAD_FILE_SIZE_LIMIT
      value: "50"
    - name: UPLOAD_FILE_BATCH_LIMIT
      value: "50"
    - name: UPLOAD_IMAGE_FILE_SIZE_LIMIT
      value: "50"
    - name: UPLOAD_VIDEO_FILE_SIZE_LIMIT
      value: "50"
    - name: UPLOAD_AUDIO_FILE_SIZE_LIMIT
      value: "50"
    - name: BATCH_UPLOAD_LIMIT
      value: "50"
    - name: WORKFLOW_FILE_UPLOAD_LIMIT
      value: "50"
    - name: CODE_MAX_STRING_LENGTH
      value: "500000"
  innerApi:
    enabled: true
    apiKey: "swLe9W2pG6bc5vfefDylbqqHfcTuq4nr2nCoQwpSEyk="

worker:
  enabled: true
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-api"
    # override appVersion in Chart.yaml if not empty
    tag: ""
  replicas: 1
  resources:
    limits:
      cpu: 3000m
      memory: 10240Mi
    requests:
      cpu: 1000m
      memory: 3414Mi
  extraEnv:
    - name: HTTP_PROXY
      value: http://************:8080
    - name: HTTPS_PROXY
      value: http://************:8080
    - name: NO_PROXY
      value: "localhost,127.0.0.1,.svc,.svc.cluster.local,dify-sandbox-svc.default.svc.cluster.local,dify-plugin-daemon-svc,dify-plugin-daemon-svc.default.svc.cluster.local,************,************,*************,10.199.139,************,***********,*************,*************,************,************,************,*************,************,***********,http://dify-sandbox-svc:8194,http://dify-unstructured-svc:8000,http://dify-plugin-daemon-svc:5002"
    # - name: UNSTRUCTURED_API_URL
    #   value: http://dify-unstructured-svc:8000/general/v0/general
    - name: INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH
      value: "5000"
    - name: REDIS_SENTINEL_SOCKET_TIMEOUT
      value: "5"
    - name: HTTP_REQUEST_MAX_READ_TIMEOUT
      value: "300"

web:
  enabled: true
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-web"
    # override appVersion in Chart.yaml if not empty
    tag: ""
  logoConfig:
    enabled: true
    image:
      repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/busybox"
      tag: "1.36.1"
    faviconIcoBase64: |
      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
    logoSitePngBase64: |
      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
  replicas: 1
  resources:
    limits:
      cpu: 2000m
      memory: 8192Mi
    requests:
      cpu: 667m
      memory: 2731Mi
  extraEnv:
    - name: INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH
      value: "5000"
    - name: TEXT_GENERATION_TIMEOUT_MS
      value: "300000"

sandbox:
  enabled: true
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-sandbox"
    tag: "0.2.12"
  replicas: 1
  resources:
    limits:
      cpu: 2000m
      memory: 8192Mi
    requests:
      cpu: 1000m
      memory: 4096Mi
  apiKey: "swLe9W2pG6bc5vfefDylbqqHfcTuq4nr2nCoQwpSEyk="
  workerTimeout: 300
  pythonRequirements: |
    requests>=2.25.1
    openpyxl>=3.0.7
    pandas>=1.3.0,<2.4.0
    langfuse==2.60.8
    docxtpl
  # 添加环境变量使用阿里云镜像源并优化安装
  extraEnv:
    - name: PIP_INDEX_URL
      value: "https://mirrors.aliyun.com/pypi/simple/"
    - name: PIP_TRUSTED_HOST
      value: "mirrors.aliyun.com"
    - name: PIP_DEFAULT_TIMEOUT
      value: "1800"  # 增加到30分钟
    - name: PIP_RETRIES
      value: "10"    # 增加重试次数
    - name: PIP_TIMEOUT
      value: "1800"  # 增加到30分钟
    - name: PYTHONUNBUFFERED
      value: "1"
    - name: PYTHONDONTWRITEBYTECODE
      value: "1"
    - name: PIP_NO_CACHE_DIR
      value: "1"     # 禁用缓存避免空间问题
    - name: PIP_DISABLE_PIP_VERSION_CHECK
      value: "1"     # 禁用版本检查
    - name: ALLOWED_SYSCALLS
      value: "0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,364,365,366,367,368,369,370,371,372,373,374,375,376,377,378,379,380,381,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,448,449,450"

enterprise:
  adminAPIsSecretKeySalt: "difyai123456"
  enabled: true
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-enterprise"
    tag: "0.11.0"
  replicas: 1
  resources:
    limits:
      cpu: 2000m
      memory: 4096Mi
    requests:
      cpu: 1000m
      memory: 2048Mi
  appSecretKey: "swLe9W2pG6bc5vfefDylbqqHfcTuq4nr2nCoQwpSEyk="
  adminAPIsSecretKeySalt: "difyai123456"
  innerApi:
    enabled: true
    apiKey: "swLe9W2pG6bc5vfefDylbqqHfcTuq4nr2nCoQwpSEyk="
  readinessProbe:
    failureThreshold: 5           # 增加为5次（原来是3次）
    httpGet:
      path: /v1/healthz
      port: 8082
      scheme: HTTP
    initialDelaySeconds: 60       # 增加为60秒（原来是30秒）
    periodSeconds: 20             # 增加为20秒（原来是10秒）
    successThreshold: 1
    timeoutSeconds: 10            # 增加为10秒（原来是3秒）
  livenessProbe:
    failureThreshold: 5           # 增加为5次（原来是3次）
    httpGet:
      path: /v1/healthz
      port: 8082
      scheme: HTTP
    initialDelaySeconds: 120      # 增加为120秒（原来是20秒）
    periodSeconds: 30             # 增加为30秒（原来是10秒）
    successThreshold: 1
    timeoutSeconds: 10            # 增加为10秒（原来是3秒）

enterpriseAudit:
  enabled: true
  image:
    # g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-audit
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-audit"
    tag: "0.11.0"
  replicas: 1
  resources: {}
  nodeSelector: {}
  affinity: {}
  tolerations: []
  readinessProbe:
    failureThreshold: 3
    httpGet:
      path: /v1/healthz
      port: 8083
      scheme: HTTP
    initialDelaySeconds: 30
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 3
  livenessProbe:
    failureThreshold: 3
    httpGet:
      path: /v1/healthz
      port: 8083
      scheme: HTTP
    initialDelaySeconds: 20
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 3
  extraEnv:
  # Apply your own Environment Variables if necessary. Below is demo.
  # extraEnv:
  #   - name: ENV_FROM_COMMUNITY1
  #     value: env123
  #   - name: ENV_FROM_COMMUNITY2
  #     value: env123
  scheduler:
    solidifyCheckCron: "0 0 */1 * *" # minutes hours day-of-month month day-of-week
    solidifyDelayDays: 90
    timeout: "600s"
  mq:
    checkInterval: "5s"
    syncThreshold: 200
    maxWait: "10s"
  serverTimeout: "10s"
  grpcServerTimeout: "10s"
  downloadTimeout: "60s"

enterpriseFrontend:
  enabled: true
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-enterprise-frontend"
    tag: "0.11.0"
  replicas: 1
  resources:
    limits:
      cpu: 1000m
      memory: 4096Mi
    requests:
      cpu: 334m
      memory: 1366Mi

ssrfProxy:
  enabled: true
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/squid"
    tag: "5.2-22.04_beta"
  replicas: 1
  resources:
    limits:
      cpu: 1000m
      memory: 4096Mi
    requests:
      cpu: 334m
      memory: 1366Mi
  extraEnv:
    - name: HTTP_PROXY
      value: http://************:8080
    - name: HTTPS_PROXY
      value: http://************:8080
    - name: NO_PROXY
      value: "localhost,127.0.0.1,.svc,.svc.cluster.local,dify-sandbox-svc.default.svc.cluster.local,dify-plugin-daemon-svc,dify-plugin-daemon-svc.default.svc.cluster.local,************,************,*************,10.199.139,************,***********,*************,*************,************,************,************,*************,************,***********,http://dify-sandbox-svc:8194,http://dify-unstructured-svc:8000,http://dify-plugin-daemon-svc:5002"
  squidConf: |
    # 基本端口和模式定义
    http_port 3128

    # 定义上游代理服务器
    cache_peer ************ parent 8080 0 no-query default

    # ACL定义
    acl no_proxy_ip dst ************** ************ ************* ************* *********** ************ *********** ************* ************* ************ ************ ************ ************* ************ *********** ***********
    acl no_proxy_domain dstdomain test-apaasgw.is.panasonic.cn apaasgw.is.panasonic.cn ookoo.is.panasonic.cn test-ookoo.is.panasonic.cn

    # 直连配置（不走代理）
    always_direct allow no_proxy_ip
    always_direct allow no_proxy_domain

    # 其他请求通过代理
    never_direct allow all

    # 允许所有访问
    http_access allow all

    ################################## Reverse Proxy Configuration ################################
    # 设置8194端口用于反向代理
    http_port 8194 accel vhost

    # 将到8194端口的请求转发到名为'sandbox'的上游服务器（需替换具体地址和端口）
    cache_peer sandbox_address parent 8194 0 no-query originserver

    # 允许所有来源的请求访问8194端口
    acl src_all src all
    http_access allow src_all

unstructured:
  enabled: true
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/unstructured-api"
  replicas: 1
  resources:
    limits:
      cpu: 1000m
      memory: 4096Mi
    requests:
      cpu: 334m
      memory: 1366Mi

plugin_daemon:
  enabled: true
  replicas: 1
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/dify-plugin-daemon"
    tag: "0.1.3-serverless"
  resources: {}
  apiKey: "swLe9W2pG6bc5vfefDylbqqHfcTuq4nr2nCoQwpSEyk="
  maxLaunchSeconds: 3600
  forceVerifyingSignature: false
  # NodePort / LoadBalancer
  serviceType: "NodePort"
  # loadbalancer or node ip
  remoteInstallHost: "127.0.0.1"
  remoteInstallPort: "5003"
  innerApiKey: QaHbTe77CtuXmsfyhR7+vRjI/+XbV1AaFy691iy+kGDv2Jvy0/eAh8Y1
  pluginExecuteTimeout: 1800
  extraEnv:
    - name: HTTP_PROXY
      value: http://************:8080
    - name: HTTPS_PROXY
      value: http://************:8080
    - name: NO_PROXY
      value: "localhost,127.0.0.1,.svc,.svc.cluster.local,dify-sandbox-svc.default.svc.cluster.local,dify-api-svc,dify-api-svc.default.svc.cluster.local,************,************,*************,10.199.139,************,***********,*************,*************,************,************,************,*************,************,***********,http://dify-sandbox-svc:8194,http://dify-unstructured-svc:8000"
    # - name: PLUGIN_MAX_EXECUTION_TIMEOUT
    #   value: "1800"
 
plugin_controller:
  replicas: 1
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/enterprise_plugin-crd"
    tag: "0.11.0"
  resources: {}

plugin_connector:
  replicas: 1
  image:
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/enterprise_plugin-connector"
    tag: "0.11.0"
  resources: {}
  apiKey: "swLe9W2pG6bc5vfefDylbqqHfcTuq4nr2nCoQwpSEyk="
  maxWaitSeconds: 3600
  customServiceAccount: ""
  runnerServiceAccount: ""
  imageRepoSecret: "image-repo-secret"
  imageRepoPrefix: "test-pissh-acr-registry.cn-shanghai.cr.aliyuncs.com/dify"
  # imageRepoType: docker / ecr
  imageRepoType: docker
  ecrRegion: "us-east-1"
  # httpProxy: "http://************:8080"
  # g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/nginx:1.27.3
  gatewayImage: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/nginx:1.27.3"
  # g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/executor:latest
  shaderImage: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/executor:latest"
  # g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/busybox:latest
  busyBoxImage: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/busybox:latest"
  awsCliImage: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/aws-cli:latest"
  generatorConf: |
    generator:
      repo: langgenius
      python:
        pipMirror: "https://mirrors.aliyun.com/pypi/simple/"
        preCompile: true
        versions:
          python3.13:
            langgenius: g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/plugin-build-base-python:3.13
          python3.12:
            langgenius: g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/plugin-build-base-python:3.12
          python3.11:
            langgenius: g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/plugin-build-base-python:3.11
          python3.10:
            langgenius: g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/plugin-build-base-python:3.10

gateway:
  replicas: 1
  image:
    # g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/enterprise_gateway
    repository: "g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/enterprise_gateway"
    tag: 0.11.0
  resources: {}
  nodeSelector: {}
  affinity: {}
  tolerations: []

minio:
  rootUser: minioadmin
  # The root password for the MinIO server. You can generate a strong password using `openssl rand -base64 32`.
  rootPassword: NzGG+ngHuwz0xzMQ1XFt8JYCAxdrhnQ8/93LCggMt00=
  replicas: 1
  mode: standalone
  resources:
    limits:
      cpu: 1000m
      memory: 2048Mi
    requests:
      cpu: 500m
      memory: 1024Mi
  persistence:
    enabled: false

###################################
# Persistence Configration
###################################
persistence:
  # The storage type support: local, s3, azure-blob, aliyun-oss, google-storage
  type: "azure-blob"
  azureBlob:
    accountName: "difytestn3"
    accountKey: "****************************************************************************************"
    containerName: "difystoragecontainer123"
    accountUrl: "https://difytestn3.blob.core.chinacloudapi.cn"

###################################
# Mail Configuration
###################################
mail:
  # Mail configuration, support: resend, smtp
  type: 'smtp'
  # default email sender from email address, if not not given specific address
  defaultSender: '<EMAIL>'
  # the api-key for resend (https://resend.com)
  resend:
    apiKey: ''
    apiUrl: https://api.resend.com
  smtp:
    server: '************'
    port: 25
    username: ''
    password: ''
    useTLS: false

###################################
# External postgres
###################################
externalPostgres:
  enabled: true
  address: ************
  port: 5432
  credentials:
    dify:
      database: "azure_dify"
      username: "dify"
      password: "Panasonic@2025"
      sslmode: "require"
    plugin_daemon:
      database: "azure_dify_plugin_daemon"
      username: "dify"
      password: "Panasonic@2025"
      sslmode: "require"
    enterprise:
      database: "azure_enterprise"
      username: "dify"
      password: "Panasonic@2025"
      sslmode: "require"
    audit:
      database: "azure_audit"
      username: "dify"
      password: "Panasonic@2025"
      sslmode: "require"

###################################
# External Redis
###################################
externalRedis:
  enabled: true
  host: "************"
  port: 6379
  username: ""
  password: "CwoVVw580Rk77O77Nmtd867elWtLN7daqAzCaEgOSZs="
  useSSL: false

###################################
# vectorDB
###################################
vectorDB:
  useExternal: true
  externalType: "qdrant"
  externalQdrant:
    endpoint: "http://************/"
    apiKey: "Xf/tLkuuyZ9Vwgvmh2ZmYt10VnmOVIvgDUf9R/vbq3I="

imagePullSecrets: []