# 添加 Helm 仓库

```shell
helm repo add dify https://g-hsod9681-helm.pkg.coding.net/dify-artifact/dify-helm
```

# 升级部署

```shell
# 安装docker
sudo su
export DOWNLOAD_URL="https://mirrors.tuna.tsinghua.edu.cn/docker-ce"
# 如您使用 curl
curl -fsSL https://raw.githubusercontent.com/docker/docker-install/master/install.sh | sh
# 如您使用 wget
wget -O- https://raw.githubusercontent.com/docker/docker-install/master/install.sh | sh
# Qdrant国内镜像源
g-hsod9681-docker.pkg.coding.net/dify-artifact/dify/qdrant:v1.7.3
# 添加 Helm 仓库
helm repo add dify https://langgenius.github.io/dify-helm
helm repo add dify-cn https://g-hsod9681-helm.pkg.coding.net/dify-artifact/dify-helm
# 查看部署历史记录
helm list -n dify
# 更新企业版 Helm 版本
helm repo update
# 查看企业版 Helm 版本
helm search repo dify/dify --versions
# 设置代理
$env:HTTP_PROXY="http://************:8080"
$env:HTTPS_PROXY="http://************:8080"
# 升级企业版
helm upgrade -n dify -i dify -f panasonic-values.yaml dify/dify
helm upgrade -n dify -i dify -f panasonic-values.yaml dify/dify --version 2.3.0
helm upgrade -n dify -i dify -f panasonic-values-dev.yaml dify/dify
helm upgrade -n dify -i dify -f panasonic-values-dev.yaml dify/dify --version 2.8.0
helm upgrade -n dify -i dify -f panasonic-values-dev.yaml dify/dify --version 3.3.0
# 查看历史版本
helm history dify -n dify
# 回退到版本8
helm rollback dify 8 -n dify
# 获取K8S认证凭证
az login
az aks get-credentials --resource-group DIFY-TEST-N3 --name difytestn3 --overwrite-existing
az aks get-credentials --resource-group DIFY-TEST-N3 --name difytestn3 --admin

helm uninstall dify -n dify

# 列出可用的配置上下文
kubectl config get-contexts
# 查看当前使用的上下文
kubectl config current-context
# 切换配置上下文
kubectl config use-context Dify-test
kubectl config use-context difytestn3
kubectl config set-cluster difytestn3 --insecure-skip-tls-verify=true
kubectl config set-cluster difytestn3-admin --insecure-skip-tls-verify=true
# 获取当前配置
kubectl config view
# 创建命名空间
kubectl create namespace dify
# 部署部署Ingress Nginx Controller
kubectl apply -f ./ingress-nginx-controller-v1.10.1.yaml
# 查看 ingress-nginx 命名空间中的所有服务
kubectl get svc -n ingress-nginx
# 维保后台初始账号
<EMAIL>
difyai123456
Cityzens9320!
# 查看节点 IP
kubectl get nodes -o wide

kubectl get pods -n dify
kubectl get pods -n dify -o wide
kubectl exec -it dify-sandbox-f9bd7ffd5-qrbhs -n dify -- /bin/bash

sandbox重启完要手动
pip install langfuse
pip install --timeout 300 -i https://mirrors.aliyun.com/pypi/simple/ langfuse==2.60.8
pip install --timeout 300 -i https://mirrors.aliyun.com/pypi/simple/ pandas
pip install --timeout 300 -i https://mirrors.aliyun.com/pypi/simple/ oss2
pip install requests openpyxl

dify-plugin-daemon-secret
修改AZURE_BLOB_STORAGE_CONNECTION_STRING后，重启daemon
DefaultEndpointsProtocol=https;AccountName=difytestn3;AccountKey=****************************************************************************************
DefaultEndpointsProtocol=https;AccountName=difytestn3;AccountKey=****************************************************************************************;EndpointSuffix=core.chinacloudapi.cn
```

# Docker 密钥
创建 `panasonic-aliyun-acr-secret` 密钥

```shell
kubectl create secret -n dify docker-registry panasonic-aliyun-acr-secret \
    --docker-server=test-pissh-acr-registry.cn-shanghai.cr.aliyuncs.com \
    --docker-username=xxx \
    --docker-password=xxx
```

创建 `dify-artifacts-token` 密钥
```shell
kubectl create secret docker-registry dify-artifacts-token \
    --docker-server=artifacts.langgenius.ai \
    --docker-username=xxx \
    --docker-password=xxx
```

# SSL证书 更新
```shell
# 1. 查看现在的panasonic-tls
kubectl describe secret panasonic-tls -n dify

# 2. 更新已有的K8S集群的panasonic-tls
kubectl create secret tls panasonic-tls --key ./tls.key --cert ./tls.crt --dry-run=client -o yaml -n dify | kubectl apply -n dify -f -

# 3. 查看是否更新
kubectl describe secret panasonic-tls -n dify

# 1.～ 3. 可以通过LENS直接修改
# 4. 
重启ingress


```

# ssrfProxy挂代理方法
```shell
# 1. 手动修改dify-squid的ConfigMap
    # 基本端口和模式定义
    http_port 3128

    # 定义上游代理服务器
    cache_peer ************ parent 8080 0 no-query default

    # ACL定义
    acl no_proxy_ip dst ************** ************ *************
    acl no_proxy_domain dstdomain test-apaasgw.is.panasonic.cn apaasgw.is.panasonic.cn

    # 直连配置（不走代理）
    always_direct allow no_proxy_ip
    always_direct allow no_proxy_domain

    # 其他请求通过代理
    never_direct allow all

    # 允许所有访问
    http_access allow all

    ################################## Reverse Proxy Configuration ################################
    # 设置8194端口用于反向代理
    http_port 8194 accel vhost

    # 将到8194端口的请求转发到名为'sandbox'的上游服务器（需替换具体地址和端口）
    cache_peer sandbox_address parent 8194 0 no-query originserver

    # 允许所有来源的请求访问8194端口
    acl src_all src all
    http_access allow src_all

# 2. 重启dify-ssrf-proxy的deployment

# 备注：ssrfProxy的values.yaml中可以不加代理的环境变量

```
# 复盘总结
```shell
# 1. telnet和redis走的是TCP协议，不适用于HTTP代理

# 2. 关于http_proxy的大小写
    # 设置了大写变量
    export HTTP_PROXY="http://proxy:port"
    export HTTPS_PROXY="http://proxy:port"

    # HTTPS 请求成功，因为 HTTPS_PROXY 可以被使用
    curl https://pisshai-east-us.openai.azure.com/

    # HTTP 请求失败，因为出于安全考虑忽略了 HTTP_PROXY
    curl http://demo.llmjp.com:8117/translations

# 3. Azure的Redis已改为内网访问

# 4. HTTP节点和自定义工具走的都是ssrfProxy，代码执行走的是sandbox，Embedding走的是worker

```

# 环境变量
```shell
# 1. CORS 配置
api
webApiCorsAllowOrigins: "*"
consoleCorsAllowOrigins: "*" 

# 2. Redis锁超时
api,worker
REDIS_SENTINEL_SOCKET_TIMEOUT:

# 3. 分段最大长度
api,worker
INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH

```

# 2025-03-21作业
```shell
api 和 worker 的 tag 改成 4fc91d526e40386b1997d51358df8542026f396c    √
helm upgrade -n dify -i dify -f panasonic-values.yaml dify/dify

web 加 INDEXING_MAX_SEGMENTATION_TOKENS_LENGTH √

HTTP_REQUEST_MAX_CONNECT_TIMEOUT=10
HTTP_REQUEST_MAX_READ_TIMEOUT=60⇒300 √
HTTP_REQUEST_MAX_WRITE_TIMEOUT=20

TEXT_GENERATION_TIMEOUT_MS=300000 加web

加Ingress
  annotations: 
    nginx.ingress.kubernetes.io/proxy-body-size: "15m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "300"
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "60"
```
