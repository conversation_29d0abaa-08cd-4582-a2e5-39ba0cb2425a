
{
    order dify_audit before reverse_proxy
}
test-ookoo-platform.is.panasonic.cn:80 {
header {
    -Server
    -X-Powered-By
    Strict-Transport-Security "max-age=********;"
}
    route {
dify_audit "redis://:CwoVVw580Rk77O77Nmtd867elWtLN7daqAzCaEgOSZs=@************:6379/0" 7
        handle /console/api/enterprise/* {
            reverse_proxy http://dify-enterprise-svc:8082
        }
        handle /console/api/account/* {
            reverse_proxy http://dify-api-svc:5001
        }
        handle /console/api/* {
            reverse_proxy http://dify-api-svc:5001
        }
        handle /v1/* {
            reverse_proxy http://dify-api-svc:5001
        }
        handle /api/enterprise/* {
            reverse_proxy http://dify-enterprise-svc:8082
        }
        handle /api/* {
            reverse_proxy http://dify-api-svc:5001
        }
        handle /e/* {
            reverse_proxy http://dify-plugin-daemon-svc:5002 {
                header_up Dify-Hook-Url {http.request.scheme}://{host}{uri}
            }
        }
        handle {
            reverse_proxy http://dify-web-svc:3000
        }
        handle_path /healthz {
            respond "OK"
        }
        handle /files/* {
            reverse_proxy http://dify-api-svc:5001
        }
    }
    encode zstd gzip
reverse_proxy {
    transport http {
        dial_timeout 10s
        keepalive 30s
        keepalive_interval 30s
    }
}
}

test-ookoo-console.is.panasonic.cn:80 {
header {
    -Server
    -X-Powered-By
    Strict-Transport-Security "max-age=********;"
}
    route {
dify_audit "redis://:CwoVVw580Rk77O77Nmtd867elWtLN7daqAzCaEgOSZs=@************:6379/0" 7
        handle /v1/audit/* {
            reverse_proxy http://dify-enterprise-audit-svc:8083
        }
        handle /admin-api/* {
            reverse_proxy http://dify-enterprise-svc:8082
        }
        handle /scim/* {
            reverse_proxy http://dify-enterprise-svc:8082
        }
        handle /v1/* {
            reverse_proxy http://dify-enterprise-svc:8082
        }
        handle /* {
            reverse_proxy http://dify-enterprise-frontend-svc:3000
        }
    }
    encode zstd gzip
    respond /healthz 200 {
        body "OK"
    }
reverse_proxy {
    transport http {
        dial_timeout 10s
        keepalive 30s
        keepalive_interval 30s
    }
}
}
test-ookoo.is.panasonic.cn:80 {
    handle /api/enterprise/* {
        reverse_proxy http://dify-enterprise-svc:8082
    }
    handle /api/* {
        reverse_proxy http://dify-api-svc:5001
    }
    handle {
        reverse_proxy http://dify-web-svc:3000
    }
    handle /v1/* {
        reverse_proxy http://dify-api-svc:5001
    }
}
